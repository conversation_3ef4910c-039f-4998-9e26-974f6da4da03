<template>
  <HerbitProfessionalLayout
    title="Generate Session Codes"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="teal">
          <form @submit.prevent="generateSessions" class="space-y-6">
              <!-- Assessment Selection -->
              <div>
                <Label for="assessmentSelect" class="text-gray-300">Select Assessment</Label>
                <select
                  id="assessmentSelect"
                  v-model="selectedAssessmentId"
                  class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  required
                >
                  <option value="" disabled>Select an assessment</option>
                  <option v-for="assessment in assessments" :key="assessment.id" :value="assessment.id">
                    {{ assessment.id }}: {{ assessment.name }}
                  </option>
                </select>
              </div>

              <!-- Usernames -->
              <div>
                <Label for="usernames" class="text-gray-300">Usernames (comma-separated)</Label>
                <textarea
                  id="usernames"
                  v-model="usernames"
                  class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent h-24"
                  placeholder="e.g. user1,user2,user3"
                  required
                ></textarea>
                <p class="text-xs text-gray-400 mt-1">Enter usernames separated by commas (no spaces).</p>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500"></div>
                <span class="ml-3 text-gray-300">Generating session codes...</span>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Results table -->
              <div v-if="sessions.length > 0" class="space-y-4">

                <!-- Sessions table -->
                <div class="overflow-x-auto">
                  <table class="w-full text-left border-collapse">
                    <thead>
                      <tr class="border-b border-gray-700">
                        <th class="py-3 px-4 text-gray-300 font-medium">Username</th>
                        <th class="py-3 px-4 text-gray-300 font-medium">Session Code</th>
                        <th class="py-3 px-4 text-gray-300 font-medium">Session ID</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(session, index) in sessions" :key="index" class="border-b border-gray-800">
                        <td class="py-3 px-4 text-white">{{ session.username }}</td>
                        <td class="py-3 px-4">
                          <span class="font-mono bg-gray-800 text-cyan-400 px-2 py-1 rounded">{{ session.sessionCode }}</span>
                        </td>
                        <td class="py-3 px-4 text-gray-300">{{ session.sessionDbId }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="sessionGenerate"
                  size="skillButton"
                  :disabled="isLoading"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                    </svg>
                    Generate Codes
                  </span>
                </Button>
              </div>
            </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const selectedAssessmentId = ref('');
const usernames = ref('');
const assessments = ref([]);
const isLoading = ref(false);
const sessions = ref([]);

// Fetch assessments from API
const fetchAssessments = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getAssessments();
    assessments.value = response.data.assessments || [];
  } catch (error) {
    logError(error, 'fetchAssessments');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch assessments'));
  } finally {
    isLoading.value = false;
  }
};

// Generate sessions via API
const generateSessions = async () => {
  if (!selectedAssessmentId.value || !usernames.value) {
    setErrorMessage('Please fill in all required fields');
    return;
  }

  isLoading.value = true;
  clearMessage();
  sessions.value = [];

  try {
    // Validate usernames format
    const usernameList = usernames.value.split(',').map(username => username.trim()).filter(username => username);
    if (usernameList.length === 0) {
      throw new Error('Please enter at least one username');
    }

    // Additional validation for usernames
    const invalidUsernames = [];
    for (const username of usernameList) {
      if (username.length < 2 || username.length > 50) {
        invalidUsernames.push(username);
      } else if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
        invalidUsernames.push(username);
      }
    }

    if (invalidUsernames.length > 0) {
      throw new Error(`Invalid usernames: ${invalidUsernames.join(', ')}. Usernames must be 2-50 characters and contain only letters, numbers, underscore, dash, or dot.`);
    }

    // Check for duplicates
    const uniqueUsernames = [...new Set(usernameList)];
    if (uniqueUsernames.length !== usernameList.length) {
      throw new Error('Duplicate usernames are not allowed');
    }

    // Validate assessment ID
    const assessmentId = parseInt(selectedAssessmentId.value);
    if (isNaN(assessmentId) || assessmentId <= 0) {
      throw new Error('Invalid assessment selected');
    }

    // Call the API to generate sessions
    const response = await api.admin.createSession({
      assessment_id: assessmentId,
      usernames: usernames.value // API expects comma-separated string
    });

    // Validate response
    if (!response.data) {
      throw new Error('Invalid response from server');
    }

    sessions.value = response.data.sessions || [];

    // Handle warnings if any sessions failed
    let messageText = response.data.message || `Successfully generated ${sessions.value.length} session codes!`;
    if (response.data.warnings) {
      messageText += `\n\nWarnings: ${response.data.warnings}`;
    }

    setSuccessMessage(messageText);

    // Clear the form on successful generation
    usernames.value = '';

  } catch (error) {
    logError(error, 'generateSessions');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while generating sessions'));
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
