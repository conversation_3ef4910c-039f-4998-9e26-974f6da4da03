<template>
  <HerbitProfessionalLayout
    title="User-wise Report"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="purple">
          <form @submit.prevent="generateReport" class="space-y-6">
            <!-- Username Input -->
            <div>
              <Label for="userName" class="text-gray-300">Username</Label>
              <Input
                id="userName"
                v-model="userName"
                placeholder="Enter username"
                required
              />
              <p class="text-xs text-gray-400 mt-1">Enter the username to generate a report for all their assessments</p>
            </div>

            <!-- Loading indicator -->
            <div v-if="isLoading" class="flex justify-center items-center py-4">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
              <span class="ml-3 text-gray-300">Generating report...</span>
            </div>

            <!-- Error/Success message -->
            <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
              <AlertDescription>{{ message }}</AlertDescription>
            </Alert>

            <!-- Download links -->
            <div v-if="reportGenerated" class="space-y-4">

              <div class="space-y-3">
                <h3 class="text-white font-medium">Download Reports:</h3>

                <!-- Base Report Download Card -->
                <Card v-if="baseReportUrl" variant="download" color="purple" size="sm" class="border-purple-500/30">
                  <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Base Report</p>
                    <p class="text-xs text-gray-400">Contains detailed assessment data for {{ userName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="baseReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                  </div>
                </Card>

                <!-- Score Report Download Card -->
                <Card v-if="scoreReportUrl" variant="download" color="indigo" size="sm" class="border-indigo-500/30">
                  <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Score Report</p>
                    <p class="text-xs text-gray-400">Contains score summaries and statistics for {{ userName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="scoreReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                  </div>
                </Card>
              </div>
            </div>

            <!-- Submit button -->
            <div class="flex justify-end">
              <Button
                type="submit"
                variant="reportGenerate"
                size="skillButton"
                :disabled="isLoading"
              >
                <span class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Generate User Report
                </span>
              </Button>
            </div>
          </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const userName = ref('');
const isLoading = ref(false);
const reportGenerated = ref(false);
const baseReportUrl = ref('');
const scoreReportUrl = ref('');

// Generate report function
const generateReport = async () => {
  isLoading.value = true;
  clearMessage();
  reportGenerated.value = false;
  baseReportUrl.value = '';
  scoreReportUrl.value = '';

  try {
    const response = await api.admin.generateReport({
      report_type: 'user_wise',
      user_name: userName.value
    });

    const data = response.data;

    if (data.message && !data.base_report && !data.score_report) {
      // No data found for the user
      setErrorMessage(data.message);
      return;
    }

    reportGenerated.value = true;
    setSuccessMessage('Report generated successfully!');

    // Create blob URLs for the reports
    if (data.base_report) {
      const blob = new Blob([data.base_report], { type: 'text/csv' });
      baseReportUrl.value = URL.createObjectURL(blob);
    }

    if (data.score_report) {
      const blob = new Blob([data.score_report], { type: 'text/csv' });
      scoreReportUrl.value = URL.createObjectURL(blob);
    }

  } catch (error) {
    logError(error, 'generateReport');
    setErrorMessage(getErrorMessage(error, 'An error occurred while generating the report'));
  } finally {
    isLoading.value = false;
  }
};
</script>
