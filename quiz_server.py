"""
Handles the creation and management of quizzes,
saving user answers, generating quiz codes, and handling quiz results.

Classes:
- None

Functions:
- convert_to_binary: Converts a shell script into a binary executable.
- get_all_question_ids: Extracts question IDs from a list of final questions.
- get_questions_by_difficulty: Filters questions based on difficulty level.
- calculate_question_score: Calculates score for a question based on difficulty and result.
- get_performance_level: Determines the performance level based on total score.
- check_and_save_answer: Checks and saves the user's answer to a question.
- save_result_to_db: Saves the user's quiz answer results to the database.
- convert_json_to_csv: Converts JSON data into CSV format.
- create_quiz: Creates a new quiz based on the provided topic and quiz name.
- get_value: Retrieves question count and time from environment variables.
- get_or_create_user: Gets or creates a user record and returns the internal ID.
- get_or_create_assessment: Gets or creates an assessment record and returns the ID.
- get_or_create_session: Gets or creates a session for the user and assessment.
- main: Starts the Flask app and compiles quiz shell scripts into binary executables.
"""

import asyncio
import csv
import io
import json
import logging
import os
import random
import re
import subprocess
import time
from datetime import datetime
from string import Template
from typing import List, Optional

import psycopg2
import psycopg2.extras
from fastapi import Depends, FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from api_client import query_model
from config import DATABASE_CONFIG
from env_validator import check_environment
from questions_logging import (
    get_session_and_assessment_details_by_code,  # Renamed from get_topic_by_code
)
from questions_logging import (
    assessment_report_by_date,
    assessment_report_by_topic,
    assessment_report_by_user,
    count_questions_for_skills,
    divide_number,
    fetch_attempted_question_ids,
    fetch_dynamic_questions_excluding_fixed,
    fetch_final_questions,
    fetch_questions_for_fixed_assessment,
    fetch_questions_for_skills,
    fetch_user_progress,
    get_assessment_by_id,
    get_assessment_description,
    get_assessment_questions_by_id,
    get_final_question_ids,
    get_questions_for_check,
    insert_final_questions_db,
    insert_quiz_creation_logs,
    insert_user_data,
)
from quiz_que_generate import ask_for_question, load_yaml_prompt

# Set up logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Create FastAPI app with metadata
app = FastAPI(
    title="Quiz API",
    description="API for quiz management and assessment",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Simple rate limiter
class RateLimiter:
    def __init__(self, requests_per_minute=60):
        self.requests_per_minute = requests_per_minute
        self.request_history = {}
        self.cleanup_interval = 60  # Cleanup old entries every minute
        self.last_cleanup = time.time()

    async def __call__(self, request: Request):
        # Clean up old entries
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            await self._cleanup(current_time)
            self.last_cleanup = current_time

        # Get client IP
        client_ip = request.client.host

        # Check if client has exceeded rate limit
        if client_ip in self.request_history:
            requests = self.request_history[client_ip]
            # Remove requests older than 1 minute
            requests = [t for t in requests if current_time - t < 60]

            if len(requests) >= self.requests_per_minute:
                raise HTTPException(
                    status_code=429, detail="Too many requests. Please try again later."
                )

            self.request_history[client_ip] = requests + [current_time]
        else:
            self.request_history[client_ip] = [current_time]

    async def _cleanup(self, current_time):
        """Remove entries older than 1 minute"""
        for ip in list(self.request_history.keys()):
            self.request_history[ip] = [
                t for t in self.request_history[ip] if current_time - t < 60
            ]
            if not self.request_history[ip]:
                del self.request_history[ip]


# Initialize rate limiter
rate_limiter = RateLimiter()

# Load allowed users from the environment variable
ALLOWED_USERS = [
    user.strip() for user in os.getenv("USERS", "").split(",") if user.strip()
]


# Define Pydantic models for request validation
class CreateQuizRequest(BaseModel):
    topic: str  # This remains the skill description(s) for question generation
    quiz_name: str
    user_id: str
    skill_ids: List[int]  # Now properly used for many-to-many
    question_selection_mode: str = "dynamic"  # 'fixed' or 'dynamic'
    duration: int = 30  # Duration in minutes


class AnswerRequest(BaseModel):
    user_id: str
    question_id: str  # This is que_id from questions table
    answer: str
    session_code: str  # Changed from quiz_code
    time_taken: Optional[int] = None  # Time taken in seconds


class SessionCodeRequest(BaseModel):  # Renamed from QuizCodeRequest
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


class UserCheckRequest(BaseModel):
    user_id: str


class SubQuizRequest(BaseModel):
    quiz_name: str


class SubQuizStatusRequest(BaseModel):
    quiz_name: str
    status: str


class FinalQuestionsRequest(BaseModel):
    question_ids: List[int]
    quiz_name: str
    assessment_id: Optional[int] = None  # For fixed assessments


class ReportRequest(BaseModel):
    report_type: str
    report_date: Optional[str] = None
    user_name: Optional[str] = None
    # This refers to assessment name or part of it
    report_topic: Optional[str] = None


class GenerateSkillQuestionsRequest(BaseModel):
    skillId: int
    skillName: str
    skillDescription: str


class SuggestSkillDescriptionRequest(BaseModel):
    skill_name: str
    existing_description: Optional[str] = None


class GenerateSessionsRequest(BaseModel):
    assessment_id: int
    user_ids: List[str]


def convert_to_binary(sh_file, file_name):
    """Convert a shell script into a binary executable."""
    try:
        command = f"shc -vrf {sh_file} -o {file_name}"
        subprocess.run(command, shell=True, check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError as e:
        logger.error("Failed to convert shell script: %s", e.stderr.decode())
        return False


def get_all_question_ids(final_questions_for_id):
    """Extract and return all question IDs from the given list of questions."""
    return [q["que_id"] for q in final_questions_for_id]


def get_questions_by_difficulty(all_questions, difficulty):
    """Filter questions based on the specified difficulty level."""
    return [q for q in all_questions if q["level"] == difficulty]


def calculate_question_score(level, result):
    """Calculate score for a single question based on difficulty level and result."""
    if result in ("timeout", "incorrect"):
        return 0

    score_mapping = {"easy": 1, "intermediate": 2, "advanced": 3}
    return score_mapping.get(level, 0)


def get_performance_level(total_score):
    """Determine performance level based on total score"""
    # Use questions_logging.get_performance_level which takes obtained and total
    # This is a fallback implementation

    # Get question counts for each difficulty level from environment variables
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

    # Max score calculation: easy(1) + intermediate(2) + advanced(3) points per question
    max_score = (easy_count * 1) + (intermediate_count * 2) + (advanced_count * 3)

    # Calculate percentage
    percentage = (total_score / max_score * 100) if max_score > 0 else 0

    if total_score == 0:
        return "Fail"
    if percentage < 33:
        return "Basic"
    if percentage < 62:
        return "Acceptable"
    if percentage < 85:
        return "Exceed Expectation"
    return "OUTSTANDING"


def check_and_save_answer(
    user_id: str,
    question_id: str,
    answer: str,
    session_code: str,
    time_taken: Optional[int] = None,
):
    """
    Check the given answer and save the result to the database.
    This function is called internally by the endpoint.
    """
    try:
        logger.info(
            f"Checking answer for session: {session_code}, user: {user_id}, question: {question_id}"
        )

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            raise HTTPException(
                status_code=404, detail={"error": "Invalid session code"}
            )

        # Always get the correct user_id from the session
        session_user_id = None
        try:
            # Get the external_id (username) for the user associated with this session
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]
                        logger.info(
                            f"Session is associated with user: {session_user_id}"
                        )
                    else:
                        logger.error(
                            f"No user found for user_id: {session_details['user_id']}"
                        )
                        raise HTTPException(
                            status_code=404,
                            detail={"error": "User not found for this session"},
                        )
        except Exception as e:
            logger.error(f"Error fetching user for session: {e}")
            raise HTTPException(
                status_code=500,
                detail={"error": f"Error fetching user for session: {str(e)}"},
            )

        # Always use the user_id from the session
        if session_user_id:
            if session_user_id != user_id:
                logger.warning(
                    f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}"
                )
            # Always use the session's user_id
            user_id = session_user_id

        assessment_name = session_details["assessment_name"]
        # The 'topic' in questions table is effectively the assessment_name_timestamp
        # We need to ensure `get_questions_for_check` uses the correct identifier.
        # Let's assume assessment_name is the correct 'topic' for questions.
        # This might need refinement if `questions.topic` is `quiz_nm_timestamp`.
        # For now, let's assume `assessment_name` is the `quiz_name` argument for `get_questions_for_check`.

        # The `assessment_name` from `sessions` table is like "UserAssessmentName_timestamp Assessment"
        # The `topic` in `questions` table is like "UserAssessmentName_timestamp"
        # We need to extract the base name.
        question_topic_identifier = assessment_name.replace(" Assessment", "")

        # For skill-based assessments, we need to look up questions by ID only
        # since questions can come from different topics/skills
        question = None

        # First try the original method (for backward compatibility)
        question = get_questions_for_check(question_topic_identifier, question_id)

        # If not found and this is a skill-based assessment, try looking up by question_id only
        if not question:
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                        cur.execute(
                            """
                            SELECT que_id, question, options, answer, level, topic
                            FROM questions
                            WHERE que_id = %s
                            """,
                            (int(question_id),),
                        )
                        row = cur.fetchone()
                        if row:
                            question = dict(row)
                            logger.info(
                                f"Found question {question_id} with topic {question.get('topic')} "
                                f"for assessment {assessment_name}"
                            )
            except Exception as e:
                logger.error(f"Error looking up question by ID: {e}")

        if not question:
            raise HTTPException(status_code=404, detail={"error": "Question not found"})

        correct_answer_key = question["answer"]
        correct_answer_value = question["options"].get(correct_answer_key, "")
        is_correct = answer.lower() == correct_answer_key.lower()

        quiz_type = "assessment"  # Always use 'assessment' instead of 'mock' or 'final'

        save_result_to_db(
            user_id=user_id,
            # que_id from the fetched question
            question_id_int=question["que_id"],
            answer=answer,
            is_correct=is_correct,
            session_code=session_code,
            time_taken=time_taken,
            # Pass derived assessment_name and quiz_type for legacy user_assessment table
            legacy_topic=question_topic_identifier,
            legacy_quiz_type=quiz_type,
            question_level=question["level"],
            question_text=str(question["question"]),
            question_options_json=json.dumps(question["options"]),
            question_correct_answer_key=correct_answer_key,
        )

        return {
            "is_correct": is_correct,
            "correct_answer_key": correct_answer_key,
            "correct_answer_value": correct_answer_value,
            "question_id": question["que_id"],  # Return the actual que_id
            "quiz_type": quiz_type,  # always 'assessment'
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error in check_and_save_answer_internal: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail={"error": str(e)})


def save_result_to_db(
    user_id: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    session_code: str,
    time_taken: Optional[int],
    legacy_topic: str,
    legacy_quiz_type: str,
    question_level: str,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
):
    """Save quiz answer results to the database using the new schema."""

    lowercased_answer = answer.lower()
    if lowercased_answer == "timeout":
        result_status = "Timeout"  # For legacy table
        # is_correct_for_score = False
    elif is_correct:
        result_status = "Correct"
    else:
        result_status = "Incorrect"
        # is_correct_for_score = False

    # Use is_correct_for_score for score calculation, is_correct for user_answers.is_correct
    score = calculate_question_score(question_level, result_status.lower())

    try:
        # For backward compatibility, insert into user_assessment
        insert_user_data(
            {
                "user_id": user_id,
                "topic": legacy_topic,  # This is assessment_name_timestamp
                "level": question_level,
                "quiz_type": legacy_quiz_type,  # mock or final
                "que_id": question_id_int,
                "question": question_text,
                "options": question_options_json,  # Already a JSON string
                "correct_answer": question_correct_answer_key,
                "user_answer": "None" if answer.lower() == "timeout" else answer,
                "result": result_status,
                "score": score,
            }
        )

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            # This should ideally not happen if check_and_save_answer validated it
            raise ValueError(
                f"Session code {session_code} not found during save_result_to_db"
            )

        internal_session_id = session_details["session_id"]

        # Insert into user_answers table
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """INSERT INTO user_answers
                       (session_id, question_id, user_answer, is_correct, score, time_taken, created_at)
                       VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                       ON CONFLICT (session_id, question_id)
                       DO UPDATE SET user_answer = EXCLUDED.user_answer,
                                     is_correct = EXCLUDED.is_correct,
                                     score = EXCLUDED.score,
                                     time_taken = EXCLUDED.time_taken""",
                    (
                        internal_session_id,
                        question_id_int,
                        answer,
                        is_correct,
                        score,
                        time_taken,
                    ),
                )
                conn.commit()

        return {"message": "Data saved successfully"}
    except Exception as e:
        logger.error(f"Error saving result: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error saving result: {str(e)}")


def convert_json_to_csv(json_data):
    """Convert JSON data to CSV format."""
    if not json_data:
        return ""
    output = io.StringIO()
    # Ensure json_data is a list of dicts
    if not isinstance(json_data, list) or not all(
        isinstance(item, dict) for item in json_data
    ):
        logger.warning(
            f"convert_json_to_csv expects a list of dicts, got {type(json_data)}"
        )
        # Attempt to handle if it's a single dict
        if isinstance(json_data, dict):
            json_data = [json_data]
        else:  # Or return empty if not convertible
            return ""

    if not json_data:  # Check again if it became an empty list
        return ""

    fieldnames = json_data[0].keys()
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    writer.writerows(json_data)
    csv_data = output.getvalue()
    output.close()
    return csv_data


async def apply_migrations():
    """Apply database migrations from the migrations directory"""
    logger.info("Checking for database migrations to apply...")

    try:
        import os

        import psycopg2

        from config import DATABASE_CONFIG

        # Connect to the database
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create migrations table if it doesn't exist
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS migrations (
                        id SERIAL PRIMARY KEY,
                        name TEXT UNIQUE NOT NULL,
                        batch INTEGER NOT NULL,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Get list of applied migrations
                cur.execute("SELECT name FROM migrations")
                applied_migrations = {row[0] for row in cur.fetchall()}

                # Get list of migration files
                migrations_dir = os.getenv("MIGRATIONS_DIR", "migrations")
                if not os.path.exists(migrations_dir):
                    logger.warning(f"Migrations directory '{migrations_dir}' not found")
                    return

                migration_files = sorted(
                    [f for f in os.listdir(migrations_dir) if f.endswith(".sql")]
                )

                # Apply new migrations
                applied_count = 0
                for migration_file in migration_files:
                    if migration_file not in applied_migrations:
                        logger.info(f"Applying migration: {migration_file}")

                        # Read migration file
                        with open(
                            os.path.join(migrations_dir, migration_file), "r"
                        ) as f:
                            sql_content = f.read()

                        # Apply migration
                        cur.execute(sql_content)

                        # Get the current highest batch number
                        cur.execute("SELECT COALESCE(MAX(batch), 0) FROM migrations")
                        current_batch = cur.fetchone()[0]
                        next_batch = current_batch + 1

                        # Record migration as applied
                        cur.execute(
                            "INSERT INTO migrations (name, batch) VALUES (%s, %s)",
                            (migration_file, next_batch),
                        )

                        applied_count += 1

                conn.commit()

                if applied_count > 0:
                    logger.info(f"Applied {applied_count} new migrations")
                else:
                    logger.info("No new migrations to apply")

    except Exception as e:
        logger.error(f"Failed to apply migrations: {str(e)}")
        raise


def get_or_create_user(user_id):
    """Get or create a user record and return the internal ID"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if user exists
                cur.execute("SELECT id FROM users WHERE external_id = %s", (user_id,))
                result = cur.fetchone()
                if result:
                    return result[0]

                # Create new user
                cur.execute(
                    "INSERT INTO users (external_id) VALUES (%s) RETURNING id",
                    (user_id,),
                )
                user_internal_id = cur.fetchone()[0]
                conn.commit()
                return user_internal_id
    except Exception as e:
        logger.error(f"Error in get_or_create_user: {e}")
        raise


def get_or_create_assessment(
    assessment_topic_name: str,
    is_final=False,
    description: Optional[str] = None,
    skill_ids: List[int] = [],
    question_selection_mode: str = "dynamic",
):
    """
    Create assessment with multiple skills.

    Args:
        assessment_topic_name: Base name for the assessment
        is_final: Whether this is a final assessment
        description: Optional description for the assessment
        skill_ids: List of skill IDs to associate with this assessment
        question_selection_mode: How questions are selected - 'fixed' or 'dynamic'

    Returns:
        The ID of the created or existing assessment
    """
    # Validate question_selection_mode
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise ValueError("question_selection_mode must be either 'fixed' or 'dynamic'")

    try:
        # Get total questions count from environment variable
        total_questions = int(os.getenv("TOTAL_QUESTIONS_COUNT", "30"))

        full_assessment_name = (
            f"{assessment_topic_name} {'Final' if is_final else 'Mock'} Assessment"
        )
        assessment_description = (
            description
            or f"{'Final' if is_final else 'Mock'} assessment for {assessment_topic_name}"
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if assessment exists
                cur.execute(
                    "SELECT id FROM assessments WHERE name = %s",
                    (full_assessment_name,),
                )
                result = cur.fetchone()

                if result:
                    assessment_id = result[0]
                    # Ensure skill associations exist
                    for skill_id in skill_ids:
                        cur.execute(
                            """INSERT INTO assessment_skills
                            (assessment_id, skill_id)
                            VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                            (assessment_id, skill_id),
                        )
                    return assessment_id

                # Create new assessment with composition that matches total_questions
                # Default composition is {"basic": 6, "intermediate": 6, "advanced": 8} which adds up to 20
                # We need to adjust it to match total_questions (20)
                composition = {"basic": 6, "intermediate": 6, "advanced": 8}

                cur.execute(
                    """INSERT INTO assessments
                       (name, description, is_final, total_questions, question_selection_mode, composition)
                       VALUES (%s, %s, %s, %s, %s, %s) RETURNING id""",
                    (
                        full_assessment_name,
                        assessment_description,
                        is_final,
                        total_questions,
                        question_selection_mode,
                        json.dumps(composition),
                    ),
                )
                assessment_id = cur.fetchone()[0]

                # Create skill associations
                for skill_id in skill_ids:
                    cur.execute(
                        """INSERT INTO assessment_skills
                        (assessment_id, skill_id)
                        VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                        (assessment_id, skill_id),
                    )

                conn.commit()

                # Log the assessment creation for new assessments only
                # Note: This function doesn't have user_id, so we'll use a default admin user
                # In a real scenario, you might want to pass user_id as a parameter
                try:
                    insert_quiz_creation_logs(
                        [
                            {
                                "user_id": "system",  # Default user for system-created assessments
                                "assessment_name": full_assessment_name,
                                "assessment_description": assessment_description,
                                "total_questions": total_questions,
                                "assessment_id": assessment_id,
                            }
                        ]
                    )
                except Exception as log_error:
                    # Don't fail the assessment creation if logging fails
                    logger.warning(f"Failed to log assessment creation: {log_error}")

                return assessment_id
    except Exception as e:
        logger.error(
            f"Error in get_or_create_assessment for '{assessment_topic_name}': {e}",
            exc_info=True,
        )
        raise


def get_or_create_session(user_id_external: str, assessment_id: int, session_code: str):
    """
    Get or create a session for the user and assessment.
    If session exists by code, it updates its status to 'in_progress' if 'pending'.
    """
    try:
        internal_user_id = get_or_create_user(user_id_external)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if session exists for this code
                cur.execute(
                    """SELECT id, user_id, assessment_id FROM sessions
                       WHERE code = %s""",
                    (session_code,),
                )
                result = cur.fetchone()
                if result:
                    (
                        session_db_id,
                        session_user_id,
                        session_assessment_id,
                    ) = result
                    # If session exists, ensure it's for the correct user and assessment
                    # This logic might need adjustment based on whether sessions can be reused
                    # or are strictly user-assessmentspecific from creation
                    if (
                        session_user_id != internal_user_id
                        or session_assessment_id != assessment_id
                    ):
                        # This case implies a code collision or misuse, which should be rare for 6-digit codes
                        # if they are globally unique for active sessions.
                        # Or, if sessions are pre-generated for specific users, this is an auth check.
                        logger.error(
                            f"Session code {session_code} mismatch: user ({session_user_id} vs {internal_user_id}) "
                            f"or assessment ({session_assessment_id} vs {assessment_id})"
                        )
                        raise HTTPException(
                            status_code=403,
                            detail="Session code mismatch or unauthorized.",
                        )

                    # Get assessment duration to calculate completed_at
                    cur.execute(
                        """SELECT duration_minutes FROM assessments
                           WHERE id = %s""",
                        (assessment_id,),
                    )
                    duration_result = cur.fetchone()
                    duration_minutes = (
                        duration_result[0] if duration_result else 30
                    )  # Default 30 minutes

                    cur.execute(
                        """UPDATE sessions SET status = 'in_progress',
                           started_at = COALESCE(started_at, CURRENT_TIMESTAMP),
                           completed_at = COALESCE(completed_at, CURRENT_TIMESTAMP + INTERVAL '%s minutes')
                           WHERE id = %s AND (status = 'pending' OR status = 'in_progress') RETURNING id""",
                        (duration_minutes, session_db_id),
                    )
                    updated_session = cur.fetchone()
                    conn.commit()
                    if updated_session:
                        return updated_session[0]
                    else:  # Session might be completed or expired
                        cur.execute(
                            "SELECT id FROM sessions WHERE id = %s", (session_db_id,)
                        )
                        return cur.fetchone()[
                            0
                        ]  # Return existing id anyway if status not pending/in_progress

                # If session code does not exist, this function should not create it.
                # Sessions are expected to be created by /generate_sessions.
                # A user attempting a quiz should use an existing session code.
                logger.error(
                    f"Session code {session_code} not found. Cannot create on-the-fly in get_or_create_session."
                )
                raise HTTPException(status_code=404, detail="Invalid session code.")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_or_create_session: {e}", exc_info=True)
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail="Error processing session.")


def expire_sessions():
    """
    Background task to mark sessions as expired if past completed_at and not completed.
    This function runs periodically to check for expired sessions.
    """
    try:
        logger.info("Running session expiry check...")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Find sessions that are past their completed_at time and still in progress
                cur.execute(
                    """UPDATE sessions
                       SET status = 'expired'
                       WHERE status = 'in_progress'
                       AND completed_at IS NOT NULL
                       AND completed_at < CURRENT_TIMESTAMP
                       RETURNING id, code""",
                )
                expired_sessions = cur.fetchall()

                if expired_sessions:
                    logger.info(
                        f"Expired {len(expired_sessions)} sessions: {[s['code'] for s in expired_sessions]}"
                    )
                else:
                    logger.debug("No sessions to expire")

                conn.commit()

    except Exception as e:
        logger.error(f"Error during session expiry check: {str(e)}", exc_info=True)


async def periodic_session_expiry():
    """
    Periodic task to run session expiry checks every 5 minutes.
    """
    while True:
        try:
            expire_sessions()
            # Wait 5 minutes before next check
            await asyncio.sleep(300)
        except Exception as e:
            logger.error(f"Error in periodic session expiry: {str(e)}")
            # Wait 1 minute before retrying on error
            await asyncio.sleep(60)


@app.on_event("startup")
async def startup_event():
    logger.info("Starting server...")

    # Validate environment variables
    if not check_environment():
        logger.error(
            "Environment validation failed. Server may not function correctly."
        )

    # Apply database migrations
    try:
        await apply_migrations()
    except Exception as e:
        logger.error(f"Migration failure: {str(e)}")
        # Continue startup even if migrations fail

    # Convert shell scripts to binaries
    user_success = convert_to_binary("quiz_user.sh", "quiz_user")
    admin_success = convert_to_binary("quiz_admin.sh", "quiz_admin")

    if user_success and admin_success:
        logger.info("Binary conversion completed successfully.")
    else:
        logger.warning("Some binary conversions failed. Check the logs for details.")

    # Start the periodic session expiry task
    asyncio.create_task(periodic_session_expiry())
    logger.info("Started periodic session expiry task")

    # Initialize database connection pool
    try:
        from config import get_connection_pool

        get_connection_pool()
        logger.info("Database connection pool initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize database connection pool: {str(e)}")


# =============================================================================
# ADMIN ENDPOINTS - Assessment Management
# =============================================================================


@app.post("/api/admin/quiz")
async def create_quiz(request: CreateQuizRequest, _: None = Depends(rate_limiter)):
    """
    Creates a new assessment based on the provided topic (skill description)
    and quiz name (user-defined assessment name). Generates questions for the assessment.

    Args:
        request (CreateQuizRequest): The request body containing:
            topic (str): The skill description to be used as the content basis for questions.
            quiz_name (str): The user-defined name for this assessment.
            user_id (str): The ID of the admin creating the quiz.
            skill_ids (list[int]): List of skill IDs to associate with this assessment.

    Returns:
        JSONResponse: A JSON response containing the ID of the created assessment,
        and a success message.
        Admins should then use the /generate_sessions endpoint to create usable session codes.

    Raises:
        HTTPException:
            - 400: If the topic/skill description is missing or no questions are found.
            - 500: If an error occurs during quiz creation.
    """
    skill_description_topic = (
        request.topic
    )  # This is the content topic from skill description
    user_defined_assessment_name = request.quiz_name
    user_id = request.user_id
    skill_ids = request.skill_ids

    if not skill_description_topic or len(skill_description_topic) < 20:
        raise HTTPException(
            status_code=400, detail="Valid skill description (min 20 chars) is required"
        )
    if not user_defined_assessment_name or len(user_defined_assessment_name) < 3:
        raise HTTPException(
            status_code=400, detail="Quiz name must be at least 3 characters"
        )

    # Validate skill IDs before proceeding
    if not request.skill_ids:
        raise HTTPException(
            status_code=400,
            detail="At least one skill ID is required for quiz creation",
        )

    # Validate skill existence and description
    valid_skills = []
    for skill_id in request.skill_ids:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT id, description FROM skills WHERE id = %s", (skill_id,)
                )
                skill = cur.fetchone()
                if not skill or not skill[1] or len(skill[1]) < 20:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Skill {skill_id} not found or invalid description",
                    )
                valid_skills.append(skill)

    # Use first skill's description as topic for question generation
    skill_description_topic = valid_skills[0][1]

    # Get parameters from request early to avoid UnboundLocalError
    question_selection_mode = request.question_selection_mode
    duration = request.duration

    # Validate question selection mode
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise HTTPException(
            status_code=400,
            detail="question_selection_mode must be either 'fixed' or 'dynamic'",
        )

    try:
        timestamp = datetime.now().strftime("%d_%m_%Y")
        # This combined name is used as the 'topic' in the questions table
        # and as the base for assessment names.
        assessment_base_name = f"{user_defined_assessment_name}_{timestamp}"

        # Skip generating questions with the model since we'll use existing questions from the selected skills
        # Instead, check if there are questions available for the selected skills
        # For dynamic assessments, exclude questions that are assigned to fixed assessments
        if question_selection_mode == "dynamic":
            # For dynamic assessments, exclude questions assigned to fixed assessments
            question_count = count_questions_for_skills(
                skill_ids, exclude_fixed_assessment_questions=True
            )
        else:
            # For fixed assessments, count all questions (they will be manually selected)
            question_count = count_questions_for_skills(
                skill_ids, exclude_fixed_assessment_questions=False
            )

        if question_count == 0:
            if question_selection_mode == "dynamic":
                raise HTTPException(
                    status_code=400,
                    detail="""No available questions found for the selected skills for dynamic assessment.
                    All questions may be assigned to fixed assessments. Please select skills with available """
                    """questions or create new questions.""",
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="""No questions found for the selected skills.
                    Please select skills with existing questions or create questions for these skills first.""",
                )

        logger.info(
            f"Found {question_count} existing questions for the selected skills"
        )

        # Since we're not generating questions, we don't need to create a CSV file
        # file_name = ""
        # csv_data = ""

        # Create a single assessment
        assessment_name = f"{assessment_base_name} Assessment"

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create the assessment
                cur.execute(
                    """
                    INSERT INTO assessments (
                        name, description, is_final, total_questions,
                        question_selection_mode, composition, duration_minutes
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (
                        assessment_name,
                        f"Assessment for {skill_description_topic}",
                        False,  # is_final is no longer relevant but kept for DB compatibility
                        30,  # Default total_questions
                        question_selection_mode,
                        json.dumps({"basic": 10, "intermediate": 10, "advanced": 10}),
                        duration,
                    ),
                )
                assessment_id = cur.fetchone()[0]

                # Create skill associations - store all skill IDs as JSON in a single row
                if skill_ids:
                    # First, delete any existing skill associations for this assessment
                    cur.execute(
                        """DELETE FROM assessment_skills WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    # Insert each skill ID individually
                    for skill_id in skill_ids:
                        cur.execute(
                            """INSERT INTO assessment_skills (assessment_id, skill_id)
                               VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                            (assessment_id, skill_id),
                        )

                conn.commit()

        # Log the assessment creation after successful creation
        insert_quiz_creation_logs(
            [
                {
                    "user_id": user_id,
                    "assessment_name": assessment_name,  # Use the full assessment name
                    # Use the assessment description
                    "assessment_description": f"Assessment for {skill_description_topic}",
                    "total_questions": question_count,  # Use the count of existing questions
                    "assessment_id": assessment_id,  # Include the assessment ID
                }
            ]
        )

        response = {
            "message": "Assessment created successfully. Please generate sessions to get usable codes.",
            "assessment_id": assessment_id,
            "assessment_base_name": assessment_base_name,
            "question_selection_mode": question_selection_mode,
            "skill_ids": skill_ids,
            "total_questions_available": question_count,
            "duration": duration,
        }
        return JSONResponse(content=response)

    except Exception as e:
        logger.error(f"Error creating quiz: {str(e)}", exc_info=True)
        error_response = {"error": str(e)}
        raise HTTPException(status_code=500, detail=error_response)


@app.get("/api/get_value")
def get_value():
    """
    Retrieve assessment question count and time from environment variables.

    Returns:
        dict: A dictionary containing the total questions count and question time.
    """
    total_count = os.getenv("TOTAL_QUESTIONS_COUNT", "30")
    question_time = os.getenv("QUESTION_TIME", "0")

    return {
        "total_questions_count": total_count,
        "question_time": question_time,
    }


@app.post("/api/check_session_code")  # Renamed from /check_quiz_code
def check_session_code(request: SessionCodeRequest):  # Renamed from QuizCodeRequest
    """
    Checks if a session code is valid and returns session/assessment details.

    Args:
        request (SessionCodeRequest): The request body containing the session_code.

    Returns:
        dict: A dictionary containing session and assessment details if the code is valid.

    Raises:
        HTTPException:
            - 400: If the session code is missing or invalid format.
            - 404: If the session code doesn't exist.
            - 500: If there's a database error.
    """
    session_code = request.session_code

    if not session_code:
        raise HTTPException(status_code=400, detail="Session code is required.")

    if not session_code.isdigit() or len(session_code) != 6:
        raise HTTPException(
            status_code=400, detail="Session code must be a 6-digit number."
        )

    try:
        session_details = get_session_and_assessment_details_by_code(session_code)

        if not session_details:
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        # Update session status to 'in_progress' if it's 'pending'
        if session_details["session_status"] == "pending":
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        # Get assessment duration to calculate completed_at
                        cur.execute(
                            """SELECT duration_minutes FROM assessments
                               WHERE id = %s""",
                            (session_details["assessment_id"],),
                        )
                        duration_result = cur.fetchone()
                        duration_minutes = (
                            duration_result[0] if duration_result else 30
                        )  # Default 30 minutes

                        cur.execute(
                            """UPDATE sessions
                               SET status = 'in_progress',
                                   started_at = CURRENT_TIMESTAMP,
                                   completed_at = CURRENT_TIMESTAMP + INTERVAL '%s minutes'
                               WHERE id = %s AND status = 'pending'""",
                            (duration_minutes, session_details["session_id"]),
                        )
                        conn.commit()
                        session_details["session_status"] = (
                            "in_progress"  # Reflect change
                        )
            except Exception as e:
                logger.error(
                    f"Error updating session status for code {session_code}: {e}"
                )
                # Proceed even if update fails, but log it.

        return {
            "session_id": session_details["session_id"],
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details[
                "assessment_name"
            ],  # e.g., "MyQuiz_timestamp Mock Assessment"
            "is_final": session_details["is_final"],
            "user_id_associated_with_session": session_details[
                "user_id"
            ],  # Internal user ID
            "session_status": session_details["session_status"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error checking session code {session_code}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


@app.get("/api/get_questions/{session_code}")  # Path parameter changed
def get_questions(
    session_code: str,  # Changed from quiz_code
    user_id: str = Query(...),  # External user_id of the quiz taker
    difficulty: str = Query("easy"),
    # Retake might be complex with new session model
    retake: bool = Query(False),
):
    """
    Fetches quiz questions based on the session code, user ID, difficulty.

    Args:
        session_code (str): The session code.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.
        (Note: retake logic might need review with session model)

    Returns:
        dict: A dictionary containing the quiz type, assessment name (as quiz_name),
              original topic (skill description), and a list of questions.

    Raises:
        HTTPException
    """
    logger.info(
        f"Getting questions for session code: {session_code}, user_id: {user_id}, difficulty: {difficulty}"
    )

    try:
        # Get session details including user_id
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        # Always get the correct user_id from the session
        session_user_id = None
        try:
            # Get the external_id (username) for the user associated with this session
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]
                        logger.info(
                            f"Session is associated with user: {session_user_id}"
                        )
                    else:
                        logger.error(
                            f"No user found for user_id: {session_details['user_id']}"
                        )
                        raise HTTPException(
                            status_code=404, detail="User not found for this session"
                        )
        except Exception as e:
            logger.error(f"Error fetching user for session: {e}")
            raise HTTPException(
                status_code=500, detail=f"Error fetching user for session: {str(e)}"
            )

        # Always use the user_id from the session
        if session_user_id:
            if user_id and session_user_id != user_id:
                logger.warning(
                    f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}"
                )
            # Always use the session's user_id
            user_id = session_user_id

        # Verify user if session is tied to a specific user (optional enhancement)
        # internal_user_id_from_session = session_details.get("user_id")
        # if internal_user_id_from_session:
        #     queried_user_internal_id = get_or_create_user(user_id) # Ensure user exists
        #     if internal_user_id_from_session != queried_user_internal_id:
        #         raise HTTPException(status_code=403, detail="User not authorized for this session.")

        # The 'topic' in questions table is assessment_base_name (e.g. UserAssessmentName_timestamp)
        # assessment_name in session_details is (e.g. UserAssessmentName_timestamp Mock Assessment)
        assessment_name_from_session = session_details["assessment_name"]
        question_topic_identifier = assessment_name_from_session.replace(
            " Mock Assessment", ""
        ).replace(" Final Assessment", "")

        is_final = session_details["is_final"]
        quiz_type = (
            "final" if is_final else "mock"
        )  # For logic requiring 'mock' or 'final' string

        # Get assessment details including question_selection_mode
        assessment_id = session_details["assessment_id"]
        assessment_details = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment_details = cur.fetchone()

        # Check if this is a fixed assessment
        is_fixed_assessment = (
            assessment_details
            and assessment_details["question_selection_mode"] == "fixed"
        )

        # For fixed assessments, get the pre-selected questions from assessment_questions table
        if is_fixed_assessment:
            all_questions_for_topic = fetch_questions_for_fixed_assessment(
                assessment_id
            )
            logger.info(
                f"Fetched {len(all_questions_for_topic)} fixed questions for assessment {assessment_id}"
            )
            if len(all_questions_for_topic) > 0:
                logger.info(
                    f"Sample fixed question: {all_questions_for_topic[0].get('question', 'N/A')[:50]}..."
                )
            else:
                logger.warning(
                    f"No fixed questions found for assessment {assessment_id}"
                )
        else:
            # For dynamic assessments, get questions based on the skills associated with the assessment
            # First, get the skill IDs associated with this assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """SELECT skill_id FROM assessment_skills
                           WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    results = cur.fetchall()
                    # Extract skill IDs from the results
                    skill_ids = []
                    if results:
                        for row in results:
                            skill_id = row[0]
                            skill_ids.append(skill_id)

            if skill_ids:
                # If we have skill IDs, fetch questions for these skills
                # For dynamic assessments, exclude questions that are assigned to fixed assessments
                logger.info(f"Fetching questions for skills: {skill_ids}")
                all_questions_for_topic = fetch_questions_for_skills(
                    skill_ids, exclude_fixed_assessment_questions=True
                )
                logger.info(
                    f"Fetched {len(all_questions_for_topic)} questions for skills: {skill_ids} "
                    f"(excluding fixed assessment questions)"
                )
                if len(all_questions_for_topic) > 0:
                    logger.info(
                        f"Sample dynamic question: {all_questions_for_topic[0].get('question', 'N/A')[:50]}..."
                    )
                else:
                    logger.warning(f"No dynamic questions found for skills {skill_ids}")
            else:
                # Fallback to the original logic if no skills are associated
                # For dynamic assessments, also exclude fixed assessment questions
                if is_final:
                    all_questions_for_topic = fetch_final_questions(
                        question_topic_identifier
                    )
                else:
                    # For dynamic assessments, fetch questions but exclude those assigned to fixed assessments
                    all_questions_for_topic = fetch_dynamic_questions_excluding_fixed(
                        question_topic_identifier
                    )
                    logger.info(
                        f"Fetched {len(all_questions_for_topic)} questions for topic: "
                        f"{question_topic_identifier} (excluding fixed assessment questions)"
                    )

        # Attempted questions are specific to the (user, assessment_base_name/topic)
        # This uses the legacy user_assessment table.
        # For new schema, it would be (session_id, question_id) from user_answers.
        # For now, let's assume fetch_attempted_question_ids works with question_topic_identifier and user_id.
        attempted_questions = (
            []
            if retake
            else fetch_attempted_question_ids(question_topic_identifier, user_id)
        )

        # For fixed assessments, we don't need to filter by final question IDs
        # For dynamic assessments, we need to handle final questions appropriately
        if is_fixed_assessment:
            # For fixed assessments, we only filter out attempted questions
            # and filter by difficulty if specified
            filtered_questions = []
            for question in all_questions_for_topic:
                # Skip attempted questions unless retake is allowed
                if question["que_id"] in attempted_questions and not retake:
                    continue

                # For fixed assessments, if difficulty is specified, filter by it
                # Otherwise, include all questions regardless of difficulty
                if difficulty != "all" and question["level"] != difficulty:
                    continue

                filtered_questions.append(question)
        else:
            # If it's a final quiz, all_questions_for_topic are already the selected final questions.
            # If it's mock, all_questions_for_topic are all questions for that base name.
            final_question_ids_for_topic = (
                get_final_question_ids(question_topic_identifier)
                if not is_final
                else []
            )

            filtered_questions = filter_questions(
                all_questions_for_topic,
                attempted_questions,
                # Only relevant for mock to exclude questions already in a final set
                final_question_ids_for_topic,
                is_final,
                difficulty,
                allow_retake=not is_final and retake,  # Retake only for mock
            )

        # Determine number of questions to return
        num_questions_to_return = 0

        if is_fixed_assessment:
            # For fixed assessments, return all filtered questions
            # This ensures we return the exact set of questions configured for this assessment
            num_questions_to_return = len(filtered_questions)
        elif is_final:
            # For final dynamic assessments, it should be based on assessment.total_questions
            # For now, let's assume it returns all available non-attempted final questions
            num_questions_to_return = len(filtered_questions)
            # A better approach for final would be to fetch assessment.total_questions
            # and ensure the user gets that many, possibly from a pre-shuffled list for the session.
        else:  # Mock quiz with dynamic questions
            # Use assessment's total_questions instead of environment variable
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            "SELECT total_questions FROM assessments WHERE id = %s",
                            (assessment_id,),
                        )
                        result = cur.fetchone()
                        if result:
                            num_questions_to_return = result[0]
                        else:
                            # Fallback to environment variable if assessment not found
                            mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                            num_questions_to_return = int(mock_q_count_env)
            except Exception as e:
                logger.error(f"Error fetching assessment total_questions: {e}")
                # Fallback to environment variable
                mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                try:
                    num_questions_to_return = int(mock_q_count_env)
                except ValueError:
                    num_questions_to_return = 10

        if not filtered_questions:
            logger.error(
                f"No filtered questions available. Original questions: {len(all_questions_for_topic)}"
            )
            raise HTTPException(
                status_code=404,  # Changed from 400 to be more specific
                detail=get_error_message(
                    is_final, question_topic_identifier, difficulty
                ),
            )

        # Sample questions if more are available than needed (especially for mock)
        if is_fixed_assessment:
            # For fixed assessments, don't sample - return all filtered questions in order
            selected_questions = filtered_questions
        elif len(filtered_questions) > num_questions_to_return:
            # For dynamic assessments with skills, try to get a balanced distribution from each skill
            if not is_fixed_assessment and skill_ids and len(skill_ids) > 1:
                # Group questions by skill
                questions_by_skill = {}
                for q in filtered_questions:
                    skill_id = q.get("skill_id")
                    if skill_id:
                        if skill_id not in questions_by_skill:
                            questions_by_skill[skill_id] = []
                        questions_by_skill[skill_id].append(q)

                # Calculate how many questions to take from each skill
                questions_per_skill = num_questions_to_return // len(questions_by_skill)
                remainder = num_questions_to_return % len(questions_by_skill)

                # Select questions from each skill
                selected_questions = []
                for skill_id, questions in questions_by_skill.items():
                    # Take extra question from skills until remainder is used up
                    skill_question_count = questions_per_skill + (
                        1 if remainder > 0 else 0
                    )
                    remainder -= 1 if remainder > 0 else 0

                    # If we have more questions than needed for this skill, sample randomly
                    if len(questions) > skill_question_count:
                        selected_questions.extend(
                            random.sample(questions, skill_question_count)
                        )
                    else:
                        selected_questions.extend(questions)

                # If we still need more questions (due to some skills having fewer questions than allocated)
                if len(selected_questions) < num_questions_to_return:
                    # Get all questions that weren't selected
                    remaining_questions = [
                        q for q in filtered_questions if q not in selected_questions
                    ]
                    # Sample from remaining questions to fill the gap
                    additional_needed = num_questions_to_return - len(
                        selected_questions
                    )
                    if remaining_questions and additional_needed > 0:
                        if len(remaining_questions) > additional_needed:
                            selected_questions.extend(
                                random.sample(remaining_questions, additional_needed)
                            )
                        else:
                            selected_questions.extend(remaining_questions)
            else:
                # For single skill or non-skill-based assessments, just sample randomly
                selected_questions = random.sample(
                    filtered_questions, num_questions_to_return
                )
        else:
            selected_questions = filtered_questions

        if not selected_questions:  # Double check after sampling
            raise HTTPException(
                status_code=404,
                detail=get_error_message(
                    is_final, question_topic_identifier, difficulty
                ),
            )

        # Fetch original skill description to pass as "topic" in response for user context
        # This requires joining assessment_skills and skills table.
        # For simplicity now, we'll pass assessment_name_from_session as topic.
        # A better "topic" would be the skill.description.
        # Let's assume `assessment_details.description` (from get_or_create_assessment) is good enough.
        # The `session_details` doesn't directly have the assessment description.
        # We can fetch it if needed:
        assessment_description_for_user = get_assessment_description(
            session_details["assessment_id"]
        )

        logger.info(
            f"Returning {len(selected_questions)} questions for session {session_code}"
        )

        return format_response(
            quiz_type,
            # Full assessment name like "MyQuiz_ts Mock Assessment"
            assessment_name_from_session,
            assessment_description_for_user
            or question_topic_identifier,  # User-facing topic
            selected_questions,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error fetching questions for session code {session_code}: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to load questions", "details": str(e)},
        )


def filter_questions(
    all_questions,
    attempted,
    final_ids_to_exclude_from_mock,
    is_final,
    difficulty,
    allow_retake=False,
):
    """Filter questions based on quiz type and difficulty."""
    if is_final:
        # For final quizzes, all_questions are already the selected final questions.
        # Filter out attempted ones.
        if difficulty != "all":
            # If a specific difficulty is requested, filter by it
            filtered_by_difficulty = get_questions_by_difficulty(
                all_questions, difficulty
            )
            return [q for q in filtered_by_difficulty if q["que_id"] not in attempted]
        else:
            # If all difficulties are requested, just filter out attempted ones
            return [q for q in all_questions if q["que_id"] not in attempted]

    # For mock quizzes
    if difficulty != "all":
        # If a specific difficulty is requested, filter by it
        questions_of_difficulty = get_questions_by_difficulty(all_questions, difficulty)
    else:
        # If all difficulties are requested, use all questions
        questions_of_difficulty = all_questions

    # Filter out attempted questions (if not retake) and questions already in a final set for this topic
    return [
        q
        for q in questions_of_difficulty
        if (allow_retake or q["que_id"] not in attempted)
        and q["que_id"] not in final_ids_to_exclude_from_mock
    ]


def get_error_message(is_final, quiz_name_base, difficulty):
    """Generate an appropriate error message."""
    if is_final:
        return f"No more final questions available for assessment: {quiz_name_base}, or you have attempted all."
    else:
        return (
            f"No new '{difficulty}' questions available for assessment: {quiz_name_base}. "
            "This could be because: 1) All questions have been attempted, "
            "2) All questions are assigned to fixed assessments, "
            "or 3) No questions exist for this difficulty level. Try another difficulty or create more questions."
        )


def format_response(
    quiz_type, assessment_full_name, user_facing_topic, selected_questions
):
    """Format response JSON."""
    return {
        "quiz_type": quiz_type,
        "quiz_name": assessment_full_name,  # e.g. "MyAssessment_timestamp Mock Assessment"
        "topic": user_facing_topic,  # Skill description or similar for user context
        "question": [
            {
                "que_id": q["que_id"],
                "question": q["question"],
                "options": q["options"],
                "level": q["level"],
            }
            for q in selected_questions
        ],
    }


@app.post("/api/check_and_save_answer")
def check_and_save_answer_endpoint(
    # Contains session_code now
    request: AnswerRequest,
    _: None = Depends(rate_limiter),
):
    """
    Checks and saves the user's answer for a specific quiz question using session_code.
    """
    # The internal check_and_save_answer function now handles fetching session details
    result = check_and_save_answer(
        user_id=request.user_id,
        question_id=request.question_id,
        answer=request.answer,
        session_code=request.session_code,
        time_taken=request.time_taken,
    )
    return JSONResponse(content=result)


@app.post("/api/submit_session")
def submit_session(request: SessionSubmissionRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code = request.session_code
        user_id = request.user_id

        logger.info(f"Submitting session {session_code} for user {user_id}")

        # Validate session code format
        if not session_code or not session_code.isdigit() or len(session_code) != 6:
            raise HTTPException(
                status_code=400, detail="Session code must be a 6-digit number."
            )

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        # Verify session is in progress
        if session_details["session_status"] != "in_progress":
            raise HTTPException(
                status_code=400,
                detail=f"Session is not in progress. Current status: {session_details['session_status']}",
            )

        # Verify user matches session
        internal_user_id = get_or_create_user(user_id)
        if session_details["user_id"] != internal_user_id:
            raise HTTPException(status_code=403, detail="User does not match session.")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Calculate total score from user_answers
                cur.execute(
                    """SELECT COALESCE(SUM(score), 0) as total_score
                       FROM user_answers
                       WHERE session_id = %s""",
                    (session_details["session_id"],),
                )
                score_result = cur.fetchone()
                total_score = score_result["total_score"] if score_result else 0

                # Update session to completed
                cur.execute(
                    """UPDATE sessions
                       SET status = 'completed',
                           score = %s,
                           completed_at = CURRENT_TIMESTAMP
                       WHERE id = %s AND status = 'in_progress'
                       RETURNING id""",
                    (total_score, session_details["session_id"]),
                )

                updated_session = cur.fetchone()
                if not updated_session:
                    raise HTTPException(
                        status_code=400, detail="Session could not be completed."
                    )

                conn.commit()

                logger.info(
                    f"Session {session_code} completed with score {total_score}"
                )

                return {
                    "message": "Session submitted successfully",
                    "session_id": session_details["session_id"],
                    "total_score": total_score,
                    "status": "completed",
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error submitting session {session_code}: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=500, detail=f"Error submitting session: {str(e)}"
        )


@app.get("/api/get_progress")
def get_progress(
    user_id: str = Query(...),  # External user_id
    assessment_name: str = Query(
        ...
    ),  # Full assessment name, e.g. "MyQuiz_timestamp Mock Assessment"
    # quiz_type is implicitly defined by assessment_name containing "Mock" or "Final"
):
    """
    Retrieves the progress of a user on a specific assessment.
    The `assessment_name` should be the full name like "Topic_timestamp Assessment".
    """
    try:
        quiz_type_str = "assessment"  # Always use 'assessment' type

        # Base assessment name (topic for user_assessment table)
        assessment_base_name = assessment_name.replace(" Assessment", "")

        # Try to get progress from new schema first (user_answers via sessions)
        try:
            internal_user_id = get_or_create_user(user_id)

            # Find assessment_id from assessment_name
            db_assessment_id = None
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        "SELECT id FROM assessments WHERE name = %s", (assessment_name,)
                    )
                    assessment_row = cur.fetchone()
                    if assessment_row:
                        db_assessment_id = assessment_row["id"]

            if not db_assessment_id:
                logger.warning(
                    f"No assessment found with name {assessment_name} for progress."
                )
                # Fall through to legacy, or raise error. Let's fall through for now.

            if db_assessment_id:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                        # Get the latest session for this user and assessment
                        cur.execute(
                            """SELECT s.id FROM sessions s
                               WHERE s.user_id = %s AND s.assessment_id = %s
                               ORDER BY s.created_at DESC LIMIT 1""",
                            (internal_user_id, db_assessment_id),
                        )
                        session_result = cur.fetchone()

                        if session_result:
                            session_id = session_result["id"]
                            cur.execute(
                                """SELECT ua.*, q.question, q.options, q.answer as correct_answer_key, q.level
                                   FROM user_answers ua
                                   JOIN questions q ON ua.question_id = q.que_id
                                   WHERE ua.session_id = %s
                                   ORDER BY ua.created_at""",
                                (session_id,),
                            )
                            answers = cur.fetchall()

                            if answers:
                                progress_data = []
                                total_score_val = 0

                                for answer_row in answers:
                                    result_text = (
                                        "Correct"
                                        if answer_row["is_correct"]
                                        else "Incorrect"
                                    )
                                    if (
                                        answer_row["user_answer"]
                                        and answer_row["user_answer"].lower()
                                        == "timeout"
                                    ):
                                        result_text = "Timeout"

                                    progress_data.append(
                                        {
                                            "question": answer_row["question"],
                                            "options": answer_row["options"],
                                            "correct_answer": answer_row[
                                                "correct_answer_key"
                                            ],  # This is the key 'a', 'b' etc.
                                            "user_answer": answer_row["user_answer"]
                                            or "None",
                                            "result": result_text,
                                            "score": answer_row["score"],
                                        }
                                    )
                                    total_score_val += answer_row["score"]

                                # Determine max possible score for this assessment
                                cur.execute(
                                    "SELECT total_questions FROM assessments WHERE id = %s",
                                    (db_assessment_id,),
                                )
                                cur.fetchone()
                                # This is total_questions, not max score. Max score depends on composition.
                                # For now, use questions_logging.get_performance_level which needs total_score.
                                # This part is tricky without knowing the exact composition of the taken quiz.
                                # The legacy get_performance_level(total_score) is simpler but less accurate.
                                user_level_val = get_performance_level(
                                    total_score_val
                                )  # Using the simpler version for now

                                return {
                                    "total_score": total_score_val,
                                    "user_level": user_level_val,
                                    "progress_report": progress_data,
                                }
        except Exception as e:
            logger.warning(
                f"Error getting progress from new schema for {user_id} on {assessment_name}, falling back: {e}",
                exc_info=True,
            )

        # Fall back to old schema (user_assessment table)
        # fetch_user_progress expects assessment_base_name and quiz_type_str
        logger.info(
            "Falling back to legacy progress for user %s, assessment base %s, type %s",
            user_id,
            assessment_base_name,
            quiz_type_str,
        )

        progress_data_legacy = fetch_user_progress(
            assessment_base_name, user_id, quiz_type_str
        )
        if not progress_data_legacy:  # If legacy also returns empty, then no progress.
            return {
                "total_score": 0,
                "user_level": "N/A",
                "progress_report": [],
                "message": "No progress found.",
            }

        logging.debug(f"Legacy Progress Data: {progress_data_legacy}")
        total_score_legacy = sum(item["score"] for item in progress_data_legacy)
        user_level_legacy = get_performance_level(total_score_legacy)  # Simpler version
        return {
            "total_score": total_score_legacy,
            "user_level": user_level_legacy,
            "progress_report": progress_data_legacy,
        }
    except Exception as e:
        logging.error(
            f"Error in get_progress for {user_id} on {assessment_name}: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail={"error": str(e)})


@app.post("/api/check_user")
def check_user(request: UserCheckRequest):
    """
    Check if a user is in the list of allowed users.
    """
    if request.user_id in ALLOWED_USERS:
        return {"status": "true"}
    return {"status": "false"}


@app.get("/api/get_question_counts")
def get_question_counts():
    """
    Fetch question counts for different difficulty levels for final assessments.
    """
    n = int(os.getenv("FINAL_QUESTION_COUNT", "20"))
    easy, intermediate, advanced = divide_number(n)
    return {"easy": easy, "intermediate": intermediate, "advanced": advanced}


@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check database connection
        import psycopg2

        from config import DATABASE_CONFIG

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()

        if result and result[0] == 1:
            return {"status": "healthy", "database": "connected"}
        else:
            logger.error("Health check: DB query did not return 1.")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "database": "error",
                    "detail": "DB query failed",
                },
            )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=503, content={"status": "unhealthy", "error": str(e)}
        )


# Skills management endpoints
class CreateSkillRequest(BaseModel):
    name: str
    description: str = ""


@app.post("/api/admin/skills")
async def create_skill(request: CreateSkillRequest, _: None = Depends(rate_limiter)):
    """Create a new skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """INSERT INTO skills (name, description)
                       VALUES (%s, %s) RETURNING id, name, description""",
                    (request.name, request.description),
                )
                skill = dict(cur.fetchone())
                conn.commit()
                return {"skill": skill, "message": "Skill created successfully"}
    except psycopg2.errors.UniqueViolation:
        raise HTTPException(
            status_code=400, detail="A skill with this name already exists"
        )
    except Exception as e:
        logger.error(f"Error creating skill: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating skill: {str(e)}")


@app.get("/api/admin/skills")
async def get_skills():
    """Get all skills"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description
                    FROM skills
                    ORDER BY name
                """
                )
                skills = [dict(row) for row in cur.fetchall()]
                return skills
    except Exception as e:
        logger.error(f"Error getting skills: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting skills: {str(e)}")


@app.get("/api/admin/skill-question-counts")
async def get_skill_question_counts():
    """Get question counts for each skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT skill_id, COUNT(*) as count
                    FROM questions
                    WHERE skill_id IS NOT NULL
                    GROUP BY skill_id
                """
                )
                counts = {row["skill_id"]: row["count"] for row in cur.fetchall()}
                return counts
    except Exception as e:
        logger.error(f"Error getting skill question counts: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting skill question counts: {str(e)}"
        )


@app.post("/api/admin/generate-skill-questions")
async def generate_skill_questions(
    request: GenerateSkillQuestionsRequest, _: None = Depends(rate_limiter)
):
    """Generate questions for a specific skill"""
    try:
        skill_id = request.skillId
        skill_name = request.skillName
        skill_description = request.skillDescription

        # Validate inputs
        if not skill_id or not skill_name or not skill_description:
            raise HTTPException(
                status_code=400, detail="Skill ID, name, and description are required"
            )

        if len(skill_description.strip()) < 20:
            raise HTTPException(
                status_code=400,
                detail="Skill description must be at least 20 characters long for effective question generation",
            )

        # Check environment variables for question generation
        model_id = os.getenv("MODEL_ID")
        if not model_id:
            raise HTTPException(
                status_code=500,
                detail="Question generation service is not properly configured. MODEL_ID is missing.",
            )

        # Check if API credentials are available
        base_url = os.getenv("BASE_URL")
        api_key = os.getenv("API_KEY")
        if not base_url or not api_key:
            raise HTTPException(
                status_code=500,
                detail="Question generation service is not properly configured. API credentials are missing.",
            )

        # Check if the skill exists
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT id FROM skills WHERE id = %s", (skill_id,))
                if not cur.fetchone():
                    raise HTTPException(
                        status_code=404, detail=f"Skill with ID {skill_id} not found"
                    )

                # Get existing questions for this skill to use as context
                cur.execute(
                    """
                    SELECT question, options, answer, level
                    FROM questions
                    WHERE skill_id = %s
                    ORDER BY time DESC
                    LIMIT 10
                """,
                    (skill_id,),
                )
                existing_questions = [dict(row) for row in cur.fetchall()]

        # Create a unique quiz name for this skill
        timestamp = datetime.now().strftime("%d_%m_%Y")
        quiz_name = f"{skill_name}_{timestamp}"

        # Log the question generation attempt
        # Truncate values to fit VARCHAR(255) constraints
        truncated_quiz_name = quiz_name[:255] if len(quiz_name) > 255 else quiz_name
        truncated_skill_description = (
            skill_description[:255]
            if len(skill_description) > 255
            else skill_description
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO quiz_creation_logs (assessment_name, assessment_description, user_id, total_questions)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (
                        truncated_quiz_name,
                        truncated_skill_description,
                        "admin",  # Default admin user
                        0,  # Will be updated after generation
                    ),
                )
                conn.commit()

        # Generate questions using the skill description
        # If existing questions are available, use them as context
        if existing_questions:
            logger.info(
                f"Generating questions for skill {skill_name} with existing questions as context"
            )
        else:
            logger.info(f"Generating questions for skill {skill_name} from scratch")

        # Call the question generation function with skill_id
        result = await ask_for_question(quiz_name, skill_description, skill_id)

        # Check if question generation was successful
        if result is None:
            # Since we can't track status in the current schema, we'll just log the failure
            logger.error(f"Failed to generate questions for skill: {skill_name}")

            raise HTTPException(
                status_code=500,
                detail="Failed to generate questions. This could be due to AI service issues, "
                "network problems, or invalid skill description. Please try again or contact support "
                "if the problem persists.",
            )

        # Get the updated question count
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT COUNT(*) FROM questions WHERE skill_id = %s", (skill_id,)
                )
                question_count = cur.fetchone()[0]

                # Update the total_questions count in the log
                cur.execute(
                    """
                    UPDATE quiz_creation_logs
                    SET total_questions = %s
                    WHERE assessment_name = %s AND user_id = %s
                    """,
                    (question_count, truncated_quiz_name, "admin"),
                )
                conn.commit()

        if question_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Question generation completed but no questions were saved. This might be due to "
                "content filtering or parsing issues. Please try again with a different skill description.",
            )

        return {
            "success": True,
            "message": f"Successfully generated {question_count} questions for skill: {skill_name}",
            "questionCount": question_count,
            "count": question_count,  # For backward compatibility
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating questions for skill: {str(e)}", exc_info=True)

        # Try to log the failure if possible
        try:
            timestamp = datetime.now().strftime("%d_%m_%Y")
            quiz_name = f"{skill_name}_{timestamp}"
            # Since we can't track status in the current schema, we'll just log the error
            logger.error(
                f"Question generation failed for skill: {skill_name}, quiz_name: {quiz_name}"
            )
        except Exception:
            pass  # Don't let logging errors mask the original error

        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during question generation: {str(e)}. "
            f"Please try again or contact support if the problem persists.",
        )


@app.post("/api/admin/suggest-skill-description")
async def suggest_skill_description(
    request: SuggestSkillDescriptionRequest, _: None = Depends(rate_limiter)
):
    """Suggest a skill description using LLM"""
    try:
        # Load the prompt template from prompt.yaml file
        description_prompt_template = await load_yaml_prompt("description_prompt")
        if not description_prompt_template:
            logger.error("Failed to load description prompt template.")
            return {
                "description": "Failed to load prompt template. Please try again or enter manually.",
                "level": "intermediate",
                "level_color": "yellow",
            }

        # Replace variables in the template
        template = Template(description_prompt_template)
        variables = {
            "skill_name": request.skill_name,
            "description": request.existing_description
            or f"A skill related to {request.skill_name}",
        }

        prompt = template.substitute(variables)

        # Get the model ID from environment variables
        model_id = os.getenv("MODEL_ID")

        # Query the model directly using the api_client
        response = await query_model(prompt, model_id)

        # Extract the description and level from the response
        logger.debug(f"Full response: {response}")

        if response and "choices" in response and len(response["choices"]) > 0:
            # Extract the content from the first choice
            choice = response["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                response_text = choice["message"]["content"].strip()
            elif "text" in choice:
                response_text = choice["text"].strip()
            else:
                logger.error(f"Unexpected response structure: {response}")
                return {
                    "description": "Failed to extract content from API response. Please try again or enter manually.",
                    "level": "intermediate",
                    "level_color": "yellow",
                }

            # Parse the response to extract improved description and level
            improved_description_match = re.search(
                r"Improved Description:\s*(.*?)(?:\n|$)", response_text
            )
            level_match = re.search(r"Level:\s*(.*?)(?:\n|$)", response_text)

            if improved_description_match:
                description = improved_description_match.group(1).strip()
            else:
                # If the specific format isn't found, use the whole response as the description
                description = response_text

            level = level_match.group(1).strip() if level_match else "intermediate"

            # Add color formatting to the level
            level_color = {
                "easy": "green",
                "intermediate": "yellow",
                "advanced": "orange",
            }.get(
                level.lower(), "yellow"
            )  # Default to yellow if level is not recognized

            return {
                "description": description,
                "level": level,
                "level_color": level_color,
            }
        else:
            logger.error(f"Invalid response structure: {response}")
            return {
                "description": "Failed to generate description. Please try again or enter manually.",
                "level": "intermediate",
                "level_color": "yellow",
            }
    except Exception as e:
        logger.error(f"Error suggesting skill description: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating skill description: {str(e)}"
        )


@app.post("/api/admin/final-questions")
async def add_final_questions(
    request: FinalQuestionsRequest, _: None = Depends(rate_limiter)
):
    """Add questions to a final assessment or a fixed assessment"""
    try:
        # Check if this is for a fixed assessment
        if request.assessment_id:
            # This is for a fixed assessment
            # First, validate the assessment exists and is a fixed assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                        (request.assessment_id,),
                    )
                    assessment = cur.fetchone()
                    if not assessment:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Assessment with ID {request.assessment_id} not found",
                        )

                    if assessment[1] != "fixed":
                        raise HTTPException(
                            status_code=400,
                            detail="Questions can only be assigned to assessments with 'fixed' question selection mode",
                        )

                    # Get the skill IDs associated with this assessment
                    cur.execute(
                        "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )
                    results = cur.fetchall()
                    if not results:
                        raise HTTPException(
                            status_code=400,
                            detail="Assessment has no associated skills",
                        )

                    # Extract skill IDs from the results
                    skill_ids = []
                    for row in results:
                        skill_id = row[0]
                        skill_ids.append(skill_id)

                    # Check if all question IDs exist and belong to the assessment's skills
                    # Use parameterized queries for both question IDs and skill IDs
                    q_placeholders = ",".join(["%s"] * len(request.question_ids))
                    s_placeholders = ",".join(["%s"] * len(skill_ids))

                    query = f"""
                        SELECT COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        AND skill_id IN ({s_placeholders})
                    """
                    # Combine both lists of parameters
                    params = request.question_ids + skill_ids
                    cur.execute(query, params)
                    count = cur.fetchone()[0]
                    if count != len(request.question_ids):
                        raise HTTPException(
                            status_code=400,
                            detail="Some question IDs do not exist or are not associated with the assessment's skills",
                        )

                    # Check if the questions meet the minimum difficulty requirements
                    cur.execute(
                        f"""
                        SELECT level, COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        GROUP BY level
                        """,
                        request.question_ids,
                    )
                    level_counts = dict(cur.fetchall())

                    # Get minimum required counts from environment variables
                    min_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                    min_intermediate = int(
                        os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")
                    )
                    min_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                    selected_easy = level_counts.get("easy", 0)
                    selected_intermediate = level_counts.get("intermediate", 0)
                    selected_advanced = level_counts.get("advanced", 0)

                    # Validate minimum requirements (allow more than minimum)
                    if (
                        selected_easy < min_easy
                        or selected_intermediate < min_intermediate
                        or selected_advanced < min_advanced
                    ):
                        raise HTTPException(
                            status_code=400,
                            detail=(
                                f"Selection must contain at least "
                                f"{min_easy} easy, {min_intermediate} intermediate, "
                                f"and {min_advanced} advanced questions (you can add more). Found: "
                                f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                                f"advanced: {selected_advanced}"
                            ),
                        )

                    # Clear existing assigned questions for this assessment
                    cur.execute(
                        "DELETE FROM assessment_questions WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )

                    # Insert the new question assignments
                    for question_id in request.question_ids:
                        cur.execute(
                            """
                            INSERT INTO assessment_questions (assessment_id, question_id)
                            VALUES (%s, %s)
                            """,
                            (request.assessment_id, question_id),
                        )

                    conn.commit()

                    return {
                        "message": f"Successfully assigned {len(request.question_ids)} questions to fixed assessment (minimum requirements: {min_easy} easy, {min_intermediate} intermediate, {min_advanced} advanced)",
                        "assessment_id": request.assessment_id,
                        "count": len(request.question_ids),
                        "difficulty_counts": {
                            "easy": selected_easy,
                            "intermediate": selected_intermediate,
                            "advanced": selected_advanced,
                        },
                    }
        else:
            # This is for a regular final assessment
            # Insert the final questions
            result = insert_final_questions_db(request.question_ids)

            if result:
                return {
                    "message": "Final questions added successfully",
                    "count": len(request.question_ids),
                }
            else:
                raise HTTPException(
                    status_code=400, detail="Failed to add final questions"
                )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding final questions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error adding final questions: {str(e)}"
        )


@app.post("/api/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate session codes for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")
        usernames = request_data.get("usernames", "")

        # Validate inputs
        if not assessment_id:
            raise HTTPException(status_code=400, detail="Assessment ID is required")

        if not usernames:
            raise HTTPException(status_code=400, detail="Usernames are required")

        # Validate assessment_id is a valid integer
        try:
            assessment_id = int(assessment_id)
        except (ValueError, TypeError):
            raise HTTPException(
                status_code=400, detail="Assessment ID must be a valid number"
            )

        # Split the comma-separated usernames and validate
        user_ids = [
            username.strip() for username in usernames.split(",") if username.strip()
        ]

        if not user_ids:
            raise HTTPException(status_code=400, detail="No valid usernames provided")

        # Validate username format (basic validation)
        invalid_usernames = []
        for username in user_ids:
            if len(username) < 2 or len(username) > 50:
                invalid_usernames.append(username)
            # Check for invalid characters (basic alphanumeric + underscore + dash)
            if not all(c.isalnum() or c in ["_", "-", "."] for c in username):
                invalid_usernames.append(username)

        if invalid_usernames:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid usernames: {', '.join(invalid_usernames)}. "
                f"Usernames must be 2-50 characters and contain only letters, numbers, underscore, dash, or dot.",
            )

        # Check for duplicate usernames
        if len(user_ids) != len(set(user_ids)):
            raise HTTPException(
                status_code=400, detail="Duplicate usernames are not allowed"
            )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Validate assessment exists and get details
                cur.execute(
                    "SELECT id, name, is_final, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Assessment with ID {assessment_id} not found",
                    )

                # For fixed assessments, check if questions are assigned
                if assessment["question_selection_mode"] == "fixed":
                    cur.execute(
                        "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                        (assessment_id,),
                    )
                    question_count = cur.fetchone()[0]
                    if question_count == 0:
                        raise HTTPException(
                            status_code=400,
                            detail="Cannot generate sessions for fixed assessment without assigned questions. "
                            "Please assign questions first.",
                        )

                # Check if any of the users already have active sessions for this assessment
                existing_sessions = []
                for username in user_ids:
                    cur.execute(
                        """
                        SELECT s.code, u.external_id
                        FROM sessions s
                        JOIN users u ON s.user_id = u.id
                        WHERE u.external_id = %s AND s.assessment_id = %s AND s.status IN ('pending', 'in_progress')
                        """,
                        (username, assessment_id),
                    )
                    existing = cur.fetchone()
                    if existing:
                        existing_sessions.append(
                            f"{username} (code: {existing['code']})"
                        )

                if existing_sessions:
                    raise HTTPException(
                        status_code=400,
                        detail=f"The following users already have active sessions for this assessment: "
                        f"{', '.join(existing_sessions)}",
                    )

                created_sessions = []
                failed_sessions = []

                for user_id_external in user_ids:
                    try:
                        user_internal_id = get_or_create_user(user_id_external)

                        # Generate a unique 6-digit session code with retry limit
                        session_code = ""
                        max_attempts = 10
                        attempts = 0

                        while attempts < max_attempts:
                            session_code = str(random.randint(100000, 999999)).zfill(6)
                            cur.execute(
                                "SELECT id FROM sessions WHERE code = %s",
                                (session_code,),
                            )
                            if not cur.fetchone():
                                break  # Unique code found
                            attempts += 1

                        if attempts >= max_attempts:
                            failed_sessions.append(
                                f"{user_id_external} (could not generate unique session code)"
                            )
                            continue

                        cur.execute(
                            """INSERT INTO sessions
                               (code, user_id, assessment_id, status, created_at)
                               VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                               RETURNING id""",
                            (
                                session_code,
                                user_internal_id,
                                assessment_id,
                                "pending",
                            ),
                        )
                        session_db_id = cur.fetchone()["id"]
                        created_sessions.append(
                            {
                                "username": user_id_external,
                                "sessionCode": session_code,
                                "sessionDbId": session_db_id,
                            }
                        )
                    except Exception as user_error:
                        logger.error(
                            f"Error creating session for user {user_id_external}: {str(user_error)}"
                        )
                        failed_sessions.append(
                            f"{user_id_external} (error: {str(user_error)})"
                        )

                conn.commit()

                # Prepare response
                response = {
                    "message": f"Generated {len(created_sessions)} session codes",
                    "sessions": created_sessions,
                }

                if failed_sessions:
                    response["warnings"] = (
                        f"Failed to create sessions for: {', '.join(failed_sessions)}"
                    )

                if not created_sessions:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}",
                    )

                return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating sessions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error generating sessions: {str(e)}. "
            f"Please try again or contact support if the problem persists.",
        )


def validate_assessment_skills(assessment_id: int, required_skill_ids: List[int]):
    """
    Validate that an assessment has all required skill associations.
    """
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            # Get the skill_id for this assessment
            cur.execute(
                "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                (assessment_id,),
            )
            result = cur.fetchone()
            if not result or not result[0]:
                return False

            # Parse the JSON array of skill IDs
            assessment_skill_ids = json.loads(result[0])

            # Check if all required skill IDs are in the assessment's skill IDs
            return all(
                skill_id in assessment_skill_ids for skill_id in required_skill_ids
            )


@app.post("/api/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on date, user, or topic"""
    try:
        if request.report_type == "date_wise" and request.report_date:
            # Date format should be DD-MM-YYYY
            base_report, score_report = assessment_report_by_date(
                request.report_date, request.quiz_type or "mock"
            )
            return {
                "base_report": base_report,
                "score_report": score_report,
                "message": f"Generated date-wise report for {request.report_date}",
            }

        elif request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(
                request.user_name, request.quiz_type or "mock"
            )
            return {
                "base_report": base_report,
                "score_report": score_report,
                "message": f"Generated user-wise report for {request.user_name}",
            }

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(
                request.report_topic, request.quiz_type or "mock"
            )
            return {
                "base_report": base_report,
                "score_report": score_report,
                "message": f"Generated topic-wise report for {request.report_topic}",
            }

        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid report type or missing required parameters",
            )

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating report: {str(e)}"
        )


@app.get("/api/admin/assessments")
async def get_assessments():
    """Get all assessments"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, created_at
                    FROM assessments
                    ORDER BY created_at DESC
                """
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Get skill IDs for each assessment
                for assessment in assessments:
                    cur.execute(
                        """
                        SELECT skill_id FROM assessment_skills
                        WHERE assessment_id = %s
                        """,
                        (assessment["id"],),
                    )
                    skill_id_row = cur.fetchone()

                    if skill_id_row and skill_id_row[0]:
                        # Parse the JSON array of skill IDs
                        assessment["skill_ids"] = json.loads(skill_id_row[0])
                    else:
                        assessment["skill_ids"] = []

                return {"assessments": assessments}
    except Exception as e:
        logger.error(f"Error fetching assessments: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching assessments: {str(e)}"
        )


@app.get("/api/admin/assessment/{assessment_id}")
async def get_assessment(assessment_id: int):
    """Get a single assessment with its questions"""
    try:
        # Use the new function from questions_logging
        assessment_dict = get_assessment_by_id(assessment_id)

        if not assessment_dict:
            raise HTTPException(
                status_code=404, detail=f"Assessment with ID {assessment_id} not found"
            )

        return assessment_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching assessment {assessment_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching assessment: {str(e)}"
        )


@app.get("/api/admin/skill-questions/{skill_id}")
async def get_skill_questions(skill_id: int):
    """Get questions for a specific skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the skill exists
                cur.execute("SELECT id, name FROM skills WHERE id = %s", (skill_id,))
                skill = cur.fetchone()
                if not skill:
                    raise HTTPException(
                        status_code=404, detail=f"Skill with ID {skill_id} not found"
                    )

                # Get all questions for this skill
                cur.execute(
                    """
                    SELECT que_id, topic, level, question, options, answer, time
                    FROM questions
                    WHERE skill_id = %s
                    ORDER BY level, time DESC
                """,
                    (skill_id,),
                )

                questions = [dict(row) for row in cur.fetchall()]

                # Return empty questions array instead of 404 when skill exists but has no questions
                return {
                    "skill_id": skill_id,
                    "skill_name": skill["name"],
                    "questions": questions,
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching questions for skill {skill_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching questions for skill: {str(e)}"
        )


@app.get("/api/admin/assessment-questions/{assessment_id}")
async def get_assessment_questions(assessment_id: int):
    """Get all available questions for an assessment based on its associated skills"""
    try:
        # Use the new function from questions_logging
        result = get_assessment_questions_by_id(assessment_id)

        if result is None:
            raise HTTPException(
                status_code=404, detail=f"Assessment with ID {assessment_id} not found"
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error fetching questions for assessment {assessment_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=500, detail=f"Error fetching questions for assessment: {str(e)}"
        )


@app.post("/api/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate a shareable link for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")

        if not assessment_id:
            raise HTTPException(status_code=400, detail="Assessment ID is required")

        # Verify the assessment exists
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, name FROM assessments WHERE id = %s", (assessment_id,)
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Assessment with ID {assessment_id} not found",
                    )

        # Generate the quiz link
        # In a production environment, you might want to use the actual domain
        base_url = "http://localhost:5173"  # Frontend URL
        quiz_link = f"{base_url}/take-quiz/{assessment_id}"

        return {
            "link": quiz_link,
            "assessment_id": assessment_id,
            "assessment_name": assessment["name"],
            "message": "Quiz link generated successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating quiz link: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating quiz link: {str(e)}"
        )


@app.get("/api/admin/assessments/{assessment_id}")
async def get_single_assessment(assessment_id: int):
    """Get a single assessment by ID for the quiz link page"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Assessment with ID {assessment_id} not found",
                    )

                return dict(assessment)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching assessment {assessment_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching assessment: {str(e)}"
        )


@app.get("/api/admin/session-user/{session_code}")
async def get_session_user(session_code: str):
    """Get the username associated with a session code"""
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise HTTPException(
                status_code=400, detail="Session code must be a 6-digit number"
            )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, check if the session exists
                cur.execute(
                    """
                    SELECT s.id, s.user_id, s.assessment_id, s.status
                    FROM sessions s
                    WHERE s.code = %s
                    """,
                    (session_code,),
                )
                session_result = cur.fetchone()

                if not session_result:
                    logger.warning(f"No session found for code: {session_code}")
                    raise HTTPException(
                        status_code=404, detail="Session code not found"
                    )

                # Now get the user details
                cur.execute(
                    """
                    SELECT u.external_id as username
                    FROM users u
                    WHERE u.id = %s
                    """,
                    (session_result["user_id"],),
                )
                user_result = cur.fetchone()

                if not user_result:
                    logger.warning(
                        f"No user found for user_id: {session_result['user_id']}"
                    )
                    raise HTTPException(
                        status_code=404, detail="User not found for this session"
                    )

                # Get the assessment details
                cur.execute(
                    """
                    SELECT a.name as assessment_name, a.is_final
                    FROM assessments a
                    WHERE a.id = %s
                    """,
                    (session_result["assessment_id"],),
                )
                assessment_result = cur.fetchone()

                if not assessment_result:
                    logger.warning(
                        f"No assessment found for assessment_id: {session_result['assessment_id']}"
                    )
                    raise HTTPException(
                        status_code=404, detail="Assessment not found for this session"
                    )

                username = user_result["username"]

                return {
                    "username": username,
                    "assessment_id": session_result["assessment_id"],
                    "assessment_name": assessment_result["assessment_name"],
                    "is_final": assessment_result["is_final"],
                    "session_status": session_result["status"],
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching session user: {str(e)}"
        )


@app.get("/api/admin/assessments-with-sessions")
async def get_assessments_with_sessions():
    """Get only assessments that have existing sessions"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT DISTINCT a.id, a.name, a.description, a.is_final,
                           a.total_questions, a.question_selection_mode,
                           a.composition, a.created_at,
                           COUNT(s.id) as session_count
                    FROM assessments a
                    INNER JOIN sessions s ON a.id = s.assessment_id
                    GROUP BY a.id, a.name, a.description, a.is_final,
                             a.total_questions, a.question_selection_mode,
                             a.composition, a.created_at
                    ORDER BY a.created_at DESC
                    """
                )
                assessments = [dict(row) for row in cur.fetchall()]

                return {"assessments": assessments}

    except Exception as e:
        logger.error(f"Error fetching assessments with sessions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching assessments with sessions: {str(e)}",
        )


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("SERVER_PORT", "8000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
